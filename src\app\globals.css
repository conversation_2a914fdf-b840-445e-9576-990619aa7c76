@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import core optimized styles */
@import url('../styles/core.css');

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
/* Critical CSS for above-the-fold content */
@layer base {
  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    font-display: swap;
    will-change: scroll-position;
  }

  /* Optimize images for better performance */
  img {
    content-visibility: auto;
    contain-intrinsic-size: 1px 1000px;
  }

  /* Optimize animations for 60fps */
  * {
    will-change: auto;
  }

  /* GPU acceleration for smooth scrolling */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
}

/* ===== WHATSAPP BUTTON ===== */
.whatsapp-elegant {
  background-color: #8B7355;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.whatsapp-elegant:hover {
  background-color: rgba(139, 115, 85, 0.9);
  transform: scale(1.05);
}

.whatsapp-float {
  position: fixed;
  bottom: 2.5rem;
  right: 2.5rem;
  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.15);
  z-index: 998;
}

@media (max-width: 768px) {
  .whatsapp-float {
    bottom: 1.5rem;
    right: 1.5rem;
  }
}

.whatsapp-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}