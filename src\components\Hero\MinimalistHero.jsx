'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import { memo } from 'react';

// Enhanced statistics with better data
const heroStats = [
  { number: '200h', label: 'YTT Certyfikacja' },
  { number: '7+', label: 'Lat Doświadczenia' },
  { number: '150+', label: 'Uczestników' },
  { number: '4.9', label: 'Średnia Ocena' }
];

const MinimalistHero = memo(() => {
  const heroRef = useRef(null);
  const [mounted, setMounted] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);

  // Optimized scroll handler with throttling
  const handleScroll = useCallback(() => {
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        setScrollY(window.scrollY);
      });
    } else {
      setScrollY(window.scrollY);
    }
  }, []);

  useEffect(() => {
    setMounted(true);

    // Delay loading animation for better perceived performance
    const timer = setTimeout(() => setIsLoaded(true), 100);

    // Optimized scroll listener with passive option
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      clearTimeout(timer);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  if (!mounted) return null;
  
  return (
    <section
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
    >
      {/* Enterprise-grade Background with Advanced Blur Effect */}
      <div
        className="absolute inset-0 z-0"
        style={{
          transform: `translateY(${scrollY * 0.3}px)`,
          willChange: 'transform',
        }}
      >
        {/* Responsive Image with Modern Formats */}
        <picture>
          <source
            media="(max-width: 768px)"
            srcSet="/images/background/bali-hero-low-res.webp"
            type="image/webp"
          />
          <source
            media="(min-width: 769px)"
            srcSet="/images/background/bali-hero-1200.avif"
            type="image/avif"
          />
          <source
            media="(min-width: 769px)"
            srcSet="/images/background/bali-hero.webp"
            type="image/webp"
          />
          <Image
            src="/images/background/bali-hero.webp"
            alt="BAKASANA - Retreaty jogi w Bali i Sri Lanka"
            fill
            className="object-cover object-center"
            priority
            sizes="(max-width: 768px) 100vw, 100vw"
            quality={85}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          />
        </picture>

        {/* Sophisticated Multi-layer Blur Overlay with Performance Optimization */}
        <div className="absolute inset-0 bg-gradient-to-b from-sanctuary/70 via-sanctuary/50 to-sanctuary/80 backdrop-blur-sm will-change-transform" />
        <div className="absolute inset-0 bg-gradient-to-r from-whisper/40 via-transparent to-whisper/40 backdrop-blur-[2px]" />
        <div className="absolute inset-0 bg-gradient-to-t from-linen/60 via-transparent to-transparent" />

        {/* Additional Enterprise Blur Effect */}
        <div className="absolute inset-0 backdrop-blur-[1px] bg-sanctuary/20" />
      </div>

      {/* Main Content with Enhanced Animations */}
      <div className={`relative z-20 text-center max-w-6xl mx-auto px-8 lg:px-12 transition-all duration-1000 ease-out ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-16'
      }`}>
        {/* Elegant Badge with Glassmorphism */}
        <div className={`inline-flex items-center gap-3 px-8 py-3 mb-8 bg-pure-white/90 backdrop-blur-sm border border-charcoal/10 shadow-lg transition-all duration-700 delay-200 hover:bg-pure-white hover:shadow-xl ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}>
          <span className="w-2 h-2 bg-charcoal rounded-full"></span>
          <span className="text-xs font-inter text-charcoal tracking-[0.3em] uppercase font-medium">
            RETREATY JOGI • BALI & SRI LANKA
          </span>
        </div>

        {/* Main Title with Enhanced Typography */}
        <h1 className={`font-cormorant font-light text-charcoal mb-6 transition-all duration-1000 delay-300 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        } text-6xl md:text-7xl lg:text-8xl xl:text-9xl tracking-[0.2em] leading-none`}>
          BAKASANA
        </h1>

        {/* Elegant Subtitle */}
        <p className={`font-cormorant italic text-charcoal/70 mb-8 transition-all duration-1000 delay-400 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        } text-xl md:text-2xl lg:text-3xl tracking-wide`}>
          ~ jóga jest drogą ciszy ~
        </p>
        
        {/* Enhanced Description */}
        <p className={`font-inter text-charcoal/80 leading-relaxed mb-12 max-w-3xl mx-auto transition-all duration-1000 delay-500 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        } text-lg md:text-xl lg:text-2xl`}>
          Odkryj transformującą moc jogi w duchowych sercach Azji. Nasze retreaty to połączenie
          tradycyjnej praktyki z luksusowym komfortem, tworząc przestrzeń dla głębokiej przemiany
          w otoczeniu niesamowitych krajobrazów Bali i Sri Lanki.
        </p>
        
        {/* Enhanced Statistics Grid */}
        <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 lg:gap-8 max-w-4xl mx-auto mb-12 transition-all duration-1000 delay-600 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          {heroStats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="text-3xl md:text-4xl lg:text-5xl font-cormorant font-medium text-charcoal mb-2 group-hover:scale-105 transition-transform duration-300">
                {stat.number}
              </div>
              <div className="text-sm md:text-base text-charcoal/60 font-inter tracking-wide">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
        
        {/* Enhanced CTA Buttons */}
        <div className={`flex flex-col sm:flex-row gap-6 justify-center items-center transition-all duration-1000 delay-700 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          {/* Ghost Button */}
          <a
            href="/harmonogram"
            className="group inline-flex items-center gap-3 px-8 py-4 bg-transparent text-charcoal border-2 border-charcoal/30 font-inter font-medium tracking-wide transition-all duration-400 hover:bg-charcoal hover:text-pure-white hover:border-charcoal hover:shadow-lg"
          >
            <span>Harmonogram</span>
            <svg className="w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
            </svg>
          </a>

          {/* Filled Button */}
          <a
            href="/kontakt"
            className="group inline-flex items-center gap-3 px-8 py-4 bg-charcoal text-pure-white font-inter font-medium tracking-wide transition-all duration-400 hover:bg-charcoal/90 hover:shadow-lg hover:scale-105"
          >
            <span>Rezerwacja</span>
            <svg className="w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </a>
        </div>
      </div>

      {/* Enhanced Side Form (Desktop Only) */}
      <div className={`hidden lg:block absolute right-8 top-1/2 transform -translate-y-1/2 z-30 transition-all duration-1000 delay-800 ${
        isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'
      }`}>
        <div className="bg-pure-white/95 backdrop-blur-md p-8 shadow-2xl border border-charcoal/10 max-w-sm">
          <h3 className="font-cormorant text-2xl text-charcoal mb-4">Zarezerwuj Konsultację</h3>
          <p className="font-inter text-charcoal/70 text-sm mb-6 leading-relaxed">
            Zostaw swój email, a skontaktujemy się z Tobą w ciągu 24 godzin.
          </p>

          <form className="space-y-4">
            <input
              type="email"
              placeholder="Twój email"
              required
              className="w-full px-4 py-3 border border-charcoal/20 focus:border-charcoal focus:outline-none transition-colors font-inter text-sm"
            />
            <button
              type="submit"
              className="w-full bg-charcoal text-pure-white py-3 px-6 font-inter font-medium tracking-wide transition-all duration-300 hover:bg-charcoal/90 hover:shadow-lg"
            >
              Kontakt
            </button>
          </form>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 transition-all duration-1000 delay-900 ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}>
        <div className="flex flex-col items-center text-charcoal/50">
          <span className="text-xs mb-3 font-inter tracking-[0.2em] uppercase font-light">
            Odkryj więcej
          </span>
          <div className="w-px h-8 bg-charcoal/20 animate-pulse"></div>
          <svg className="w-5 h-5 mt-2 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </section>
  );
});

MinimalistHero.displayName = 'MinimalistHero';

export default MinimalistHero;