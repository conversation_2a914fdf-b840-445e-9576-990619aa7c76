'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import PerformantWhatsApp from './PerformantWhatsApp';
import { NavLink } from '@/components/ui/UnifiedTypography';
import { mainNavItems } from '@/data/navigationLinks';
import { useSectionDetector } from '@/components/ui/ThemeProvider';

export default function AdaptiveNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [isLightSection, setIsLightSection] = useState(true);
  const pathname = usePathname();
  const currentSection = useSectionDetector();

  // Update section mode based on detector
  useEffect(() => {
    setIsLightSection(currentSection === 'light');
  }, [currentSection]);

  const handleScroll = useCallback(() => {
    const currentScrollY = window.scrollY;
    
    // More aggressive throttling for better performance
    if (Math.abs(currentScrollY - lastScrollY) < 10) return;
    
    // Use requestAnimationFrame for smooth updates
    requestAnimationFrame(() => {
      // Hide navbar on scroll down, show on scroll up
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
        setActiveDropdown(null);
      } else {
        setIsVisible(true);
      }
      
      setScrolled(currentScrollY > 20);
      setLastScrollY(currentScrollY);
    });
  }, [lastScrollY]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Close mobile menu on route change
  useEffect(() => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  }, [pathname]);

  const isActiveLink = useCallback((href) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  }, [pathname]);

  const handleDropdownToggle = useCallback((index) => {
    setActiveDropdown(activeDropdown === index ? null : index);
  }, [activeDropdown]);

  const handleMouseEnter = useCallback((index) => {
    if (window.innerWidth >= 1024) {
      setActiveDropdown(index);
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (window.innerWidth >= 1024) {
      setActiveDropdown(null);
    }
  }, []);

  // Dynamic navbar classes based on scroll and section
  const navbarClasses = useMemo(() => {
    let classes = `fixed top-0 left-0 right-0 z-50 transition-all duration-700 ease-out navbar-adaptive ${
      isVisible ? 'transform translate-y-0' : 'transform -translate-y-full'
    }`;

    if (scrolled) {
      // Scrolled state - always dark mode with backdrop
      classes += ' navbar-scrolled navbar-dark-mode';
    } else {
      // Top of page - adapt to section
      if (isLightSection) {
        classes += ' navbar-light-mode';
      } else {
        classes += ' navbar-dark-mode';
      }
    }

    return classes;
  }, [isVisible, scrolled, isLightSection]);

  return (
    <>
      <nav className={navbarClasses}>
        <div className="mx-auto px-6 lg:px-12 xl:px-16">
          <div className="flex items-center justify-between h-20">
            
            {/* Logo - Adaptive colors */}
            <Link 
              href="/" 
              className="group relative font-cormorant font-light transition-all duration-700 hover:opacity-80"
              style={{ 
                fontSize: '24px',
                letterSpacing: '2px',
                fontWeight: '300'
              }}
            >
              BAKASANA
              <span className="absolute -bottom-1 left-0 w-0 h-[1px] bg-current transition-all duration-500 group-hover:w-full opacity-60"></span>
            </Link>

            {/* Desktop Menu */}
            <div className="hidden lg:flex items-center space-x-12">
              {mainNavItems.slice(1).map((item, index) => (
                <div 
                  key={item.href} 
                  className="relative group"
                  onMouseEnter={() => handleMouseEnter(index)}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link
                    href={item.href}
                    className="relative group flex items-center space-x-2 py-2"
                  >
                    <NavLink 
                      active={isActiveLink(item.href)}
                      className={`nav-link transition-all duration-300 hover:scale-105 ${
                        isActiveLink(item.href) ? 'active' : ''
                      } ${
                        item.highlight 
                          ? 'text-terra font-medium bg-terra/10 px-3 py-1 rounded-full' 
                          : ''
                      }`}
                    >
                      {item.label}
                    </NavLink>
                    
                    {/* Dropdown arrow */}
                    {item.dropdown && (
                      <svg 
                        className={`w-3 h-3 transition-all duration-300 ${
                          activeDropdown === index ? 'rotate-180' : ''
                        }`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                      </svg>
                    )}
                  </Link>

                  {/* Enhanced Dropdown Menu */}
                  {item.dropdown && (
                    <div className={`absolute top-full left-1/2 transform -translate-x-1/2 mt-6 
                      bg-white/98 backdrop-blur-xl shadow-premium-shadow border border-enterprise-brown/10
                      transition-all duration-500 min-w-[320px] rounded-2xl overflow-hidden
                      before:absolute before:top-0 before:left-1/2 before:transform before:-translate-x-1/2 before:-translate-y-1 
                      before:w-4 before:h-4 before:bg-white/98 before:rotate-45 before:border-l before:border-t before:border-enterprise-brown/10
                      ${activeDropdown === index 
                        ? 'opacity-100 visible translate-y-0 scale-100' 
                        : 'opacity-0 invisible translate-y-4 scale-95 pointer-events-none'
                      }`}
                    >
                      <div className="py-6">
                        {item.dropdown.map((section, sectionIndex) => (
                          <div key={sectionIndex} className={sectionIndex > 0 ? 'mt-6 pt-6 border-t border-enterprise-brown/10' : ''}>
                            <div className="px-6 mb-3">
                              <h4 className="font-cormorant font-light text-enterprise-brown text-xl tracking-wide flex items-center">
                                <span className="w-2 h-2 bg-enterprise-brown/30 rounded-full mr-3"></span>
                                {section.header}
                              </h4>
                            </div>
                            <div className="space-y-1">
                              {section.items.map((dropdownItem, itemIndex) => (
                                <Link
                                  key={dropdownItem.href}
                                  href={dropdownItem.href}
                                  className="block px-6 py-3 hover:bg-whisper/70 transition-all duration-300 group relative overflow-hidden"
                                  style={{ animationDelay: `${itemIndex * 50}ms` }}
                                >
                                  <div className="absolute left-0 top-0 w-1 h-full bg-enterprise-brown transform scale-y-0 group-hover:scale-y-100 transition-transform duration-300 origin-top"></div>
                                  <NavLink className="text-sm group-hover:text-enterprise-brown group-hover:translate-x-2 transition-all duration-300">
                                    {dropdownItem.label}
                                  </NavLink>
                                </Link>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* WhatsApp Button - Desktop */}
            <div className="hidden lg:block">
              <PerformantWhatsApp 
                size="md"
                variant="button"
                className="hover:scale-105 transition-transform duration-300"
              />
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-3 transition-all duration-300"
              aria-label="Toggle menu"
            >
              <div className="w-6 h-6 flex flex-col justify-center items-center space-y-1">
                <span className={`w-6 h-[1px] bg-current transition-all duration-300 transform ${
                  isMenuOpen ? 'rotate-45 translate-y-[4px]' : ''
                }`}></span>
                <span className={`w-6 h-[1px] bg-current transition-all duration-300 ${
                  isMenuOpen ? 'opacity-0' : ''
                }`}></span>
                <span className={`w-6 h-[1px] bg-current transition-all duration-300 transform ${
                  isMenuOpen ? '-rotate-45 -translate-y-[4px]' : ''
                }`}></span>
              </div>
            </button>
          </div>
        </div>

        {/* Enhanced Mobile Menu */}
        <div className={`lg:hidden absolute top-full left-0 right-0 
          bg-white/98 backdrop-blur-[30px] border-b border-enterprise-brown/10
          transition-all duration-500 ${
          isMenuOpen 
            ? 'opacity-100 translate-y-0 max-h-screen' 
            : 'opacity-0 -translate-y-4 max-h-0 pointer-events-none overflow-hidden'
        }`}>
          <div className="px-6 py-8 space-y-8">
            {mainNavItems.slice(1).map((item, index) => (
              <div 
                key={item.href} 
                className="space-y-3"
                style={{ 
                  animation: isMenuOpen ? `fadeInUp 0.6s ease-out ${index * 100}ms both` : 'none'
                }}
              >
                <div className="flex items-center justify-between group">
                  <Link
                    href={item.href}
                    onClick={() => !item.dropdown && setIsMenuOpen(false)}
                    className="flex-1 relative"
                  >
                    <NavLink 
                      active={isActiveLink(item.href)} 
                      className={`text-lg transition-all duration-300 ${
                        item.highlight 
                          ? 'text-terra font-medium bg-terra/10 px-4 py-2 rounded-full inline-block' 
                          : ''
                      }`}
                    >
                      {item.label}
                      {isActiveLink(item.href) && !item.highlight && (
                        <span className="inline-block ml-3 w-2 h-2 bg-enterprise-brown rounded-full opacity-80 animate-pulse"></span>
                      )}
                    </NavLink>
                    
                    {/* Mobile active indicator */}
                    {isActiveLink(item.href) && !item.highlight && (
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 w-1 h-6 bg-enterprise-brown rounded-full"></div>
                    )}
                  </Link>
                  
                  {/* Mobile dropdown toggle */}
                  {item.dropdown && (
                    <button
                      onClick={() => handleDropdownToggle(index)}
                      className="p-3 text-enterprise-brown/60 hover:text-enterprise-brown hover:bg-whisper/50 rounded-full transition-all duration-300"
                    >
                      <svg 
                        className={`w-5 h-5 transition-transform duration-300 ${
                          activeDropdown === index ? 'rotate-180' : ''
                        }`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  )}
                </div>
                
                {/* Mobile dropdown content */}
                {item.dropdown && (
                  <div className={`pl-6 space-y-4 transition-all duration-500 ${
                    activeDropdown === index 
                      ? 'opacity-100 max-h-96 translate-y-0' 
                      : 'opacity-0 max-h-0 -translate-y-2 overflow-hidden'
                  }`}>
                    {item.dropdown.map((section, sectionIndex) => (
                      <div key={sectionIndex} className="space-y-3">
                        <div className="flex items-center">
                          <div className="w-3 h-[1px] bg-enterprise-brown/30 mr-3"></div>
                          <h5 className="font-cormorant text-enterprise-brown text-base font-light tracking-wide">
                            {section.header}
                          </h5>
                        </div>
                        <div className="space-y-2 pl-6">
                          {section.items.map((dropdownItem) => (
                            <Link
                              key={dropdownItem.href}
                              href={dropdownItem.href}
                              className="block py-2 text-charcoal-light hover:text-enterprise-brown transition-colors duration-300"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              {dropdownItem.label}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </nav>

      {/* Custom styles for adaptive navbar */}
      <style jsx>{`
        /* Navbar Chameleon Styles */
        .navbar-adaptive {
          background: transparent;
          transition: all 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
          backdrop-filter: blur(0px);
        }

        /* Light mode - for light sections */
        .navbar-light-mode {
          color: var(--charcoal);
        }
        .navbar-light-mode .logo { 
          filter: invert(1); 
        }
        .navbar-light-mode a { 
          color: var(--charcoal); 
        }

        /* Dark mode - for dark sections or scrolled */
        .navbar-dark-mode,
        .navbar-scrolled {
          background: rgba(44, 44, 44, 0.95);
          backdrop-filter: blur(20px);
          color: var(--sanctuary);
          box-shadow: 0 2px 30px rgba(0, 0, 0, 0.1);
        }

        /* Active page indicator */
        .nav-link {
          position: relative;
        }

        .nav-link.active::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 24px;
          height: 2px;
          background: var(--enterprise-brown);
          border-radius: 2px;
        }

        /* Fade in animation for mobile menu */
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </>
  );
}