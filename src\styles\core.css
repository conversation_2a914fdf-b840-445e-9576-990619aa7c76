/* =============================================
   🏛️ BAKASANA CORE STYLES - OPTIMIZED
   Wszystkie niezbędne style w jednym pliku
   ============================================= */

/* ===== CSS VARIABLES ===== */
:root {
  /* Brand Colors */
  --sanctuary: #FDFCF8;
  --charcoal: #3A3A3A;
  --charcoal-light: #8A8A8A;
  --enterprise-brown: #8B7355;
  --sage: #8B9A8C;
  --ash: #E8E6E2;
  --terra: #B8956A;
  --temple-gold: #D4AF37;
  --pearl: rgba(253, 252, 248, 0.8);
  
  /* Typography */
  --font-cormorant: 'Cormorant Garamond', serif;
  --font-inter: 'Inter', sans-serif;
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: auto; /* Disable smooth scroll for better performance */
}

body {
  font-family: var(--font-inter);
  background-color: var(--sanctuary);
  color: var(--charcoal);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== LEGACY HERO STYLES (Kept for compatibility) ===== */
/* Note: New MinimalistHero uses Tailwind classes for better performance */

/* Legacy hero styles removed - using Tailwind classes in new MinimalistHero */

/* Legacy hero styles removed - using modern Tailwind approach */
/* Legacy hero form styles removed - using modern Tailwind approach */

/* Legacy form styles removed - using modern Tailwind approach */

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* Legacy responsive hero styles removed - using Tailwind responsive classes */

/* ===== ACCESSIBILITY & MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== SKIP LINKS ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  transition: top 300ms ease;
}

.skip-link:focus {
  top: 8px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}