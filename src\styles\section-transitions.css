/**
 * 💫 SECTION TRANSITIONS - Płynn<PERSON>
 * Miękkie gradacje między jasnymi i ciemnymi sekcjami
 */

/* =============================================
   SECTION TRANSITION BASE
   ============================================= */

.section-transition {
  position: relative;
  margin-top: -100px;
  padding-top: 150px;
  z-index: 1;
}

.section-transition::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(
    to bottom,
    var(--sanctuary) 0%,
    transparent 100%
  );
  pointer-events: none;
  z-index: -1;
}

/* =============================================
   LIGHT TO DARK TRANSITIONS
   ============================================= */

/* Przejście z jasnej do ciemnej sekcji */
.transition-light-to-dark {
  position: relative;
  background: linear-gradient(
    180deg,
    var(--sanctuary) 0%,
    rgba(253, 252, 248, 0.8) 30%,
    rgba(42, 39, 36, 0.1) 70%,
    var(--charcoal) 100%
  );
  padding: 100px 0;
  margin: -50px 0;
}

.transition-light-to-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: 
    radial-gradient(ellipse at top, rgba(139, 115, 85, 0.05) 0%, transparent 70%),
    radial-gradient(ellipse at bottom, rgba(42, 39, 36, 0.1) 0%, transparent 70%);
  pointer-events: none;
  z-index: 1;
}

/* =============================================
   DARK TO LIGHT TRANSITIONS
   ============================================= */

/* Przejście z ciemnej do jasnej sekcji */
.transition-dark-to-light {
  position: relative;
  background: linear-gradient(
    180deg,
    var(--charcoal) 0%,
    rgba(42, 39, 36, 0.8) 30%,
    rgba(253, 252, 248, 0.1) 70%,
    var(--sanctuary) 100%
  );
  padding: 100px 0;
  margin: -50px 0;
}

.transition-dark-to-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: 
    radial-gradient(ellipse at top, rgba(42, 39, 36, 0.1) 0%, transparent 70%),
    radial-gradient(ellipse at bottom, rgba(139, 115, 85, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: 1;
}

/* =============================================
   ORGANIC WAVE TRANSITIONS
   ============================================= */

/* Organiczne fale między sekcjami */
.wave-transition {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.wave-transition::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: var(--sanctuary);
  clip-path: polygon(0 0, 100% 0, 100% 60%, 0 100%);
  z-index: 1;
}

.wave-transition::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: var(--charcoal);
  clip-path: polygon(0 40%, 100% 0, 100% 100%, 0 100%);
  z-index: 0;
}

/* Odwrócona fala */
.wave-transition--reverse::before {
  clip-path: polygon(0 40%, 100% 0, 100% 100%, 0 100%);
  background: var(--charcoal);
}

.wave-transition--reverse::after {
  clip-path: polygon(0 0, 100% 0, 100% 60%, 0 100%);
  background: var(--sanctuary);
}

/* =============================================
   GRADIENT OVERLAYS
   ============================================= */

/* Subtelne gradient overlay dla sekcji */
.section-gradient-overlay {
  position: relative;
}

.section-gradient-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(139, 115, 85, 0.02) 0%,
    transparent 50%,
    rgba(212, 165, 116, 0.02) 100%
  );
  pointer-events: none;
  z-index: 1;
}

/* Ciemny gradient overlay */
.section-gradient-overlay--dark::before {
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.1) 0%,
    transparent 50%,
    rgba(42, 39, 36, 0.05) 100%
  );
}

/* =============================================
   BREATHING SECTIONS
   ============================================= */

/* Oddychające sekcje z subtelną animacją */
.breathing-section {
  animation: breathingSection 8s ease-in-out infinite;
}

@keyframes breathingSection {
  0%, 100% {
    background-color: var(--sanctuary);
  }
  50% {
    background-color: rgba(253, 252, 248, 0.98);
  }
}

/* Ciemna wersja oddychającej sekcji */
.breathing-section--dark {
  animation: breathingSectionDark 8s ease-in-out infinite;
}

@keyframes breathingSectionDark {
  0%, 100% {
    background-color: var(--charcoal);
  }
  50% {
    background-color: rgba(42, 39, 36, 0.95);
  }
}

/* =============================================
   PARALLAX TRANSITIONS
   ============================================= */

/* Parallax efekt dla przejść */
.parallax-transition {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.parallax-transition-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120%;
  background: linear-gradient(
    180deg,
    var(--sanctuary) 0%,
    rgba(253, 252, 248, 0.5) 50%,
    var(--charcoal) 100%
  );
  will-change: transform;
}

/* =============================================
   RESPONSIVE ADAPTATIONS
   ============================================= */

@media (max-width: 1024px) {
  .section-transition {
    margin-top: -75px;
    padding-top: 125px;
  }
  
  .section-transition::before {
    height: 150px;
  }
  
  .transition-light-to-dark,
  .transition-dark-to-light {
    padding: 75px 0;
    margin: -25px 0;
  }
  
  .wave-transition {
    height: 100px;
  }
}

@media (max-width: 768px) {
  .section-transition {
    margin-top: -50px;
    padding-top: 100px;
  }
  
  .section-transition::before {
    height: 100px;
  }
  
  .transition-light-to-dark,
  .transition-dark-to-light {
    padding: 50px 0;
    margin: -25px 0;
  }
  
  .wave-transition {
    height: 75px;
  }
  
  .parallax-transition {
    height: 150px;
  }
}

@media (max-width: 480px) {
  .section-transition {
    margin-top: -25px;
    padding-top: 75px;
  }
  
  .section-transition::before {
    height: 75px;
  }
  
  .wave-transition {
    height: 50px;
  }
  
  .parallax-transition {
    height: 100px;
  }
}

/* =============================================
   ACCESSIBILITY & PERFORMANCE
   ============================================= */

@media (prefers-reduced-motion: reduce) {
  .breathing-section,
  .breathing-section--dark {
    animation: none;
  }
  
  .parallax-transition-layer {
    will-change: auto;
  }
  
  .section-transition,
  .transition-light-to-dark,
  .transition-dark-to-light {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .section-gradient-overlay::before,
  .section-gradient-overlay--dark::before {
    display: none;
  }
  
  .transition-light-to-dark {
    background: linear-gradient(
      180deg,
      #ffffff 0%,
      #cccccc 50%,
      #000000 100%
    );
  }
  
  .transition-dark-to-light {
    background: linear-gradient(
      180deg,
      #000000 0%,
      #333333 50%,
      #ffffff 100%
    );
  }
}

/* =============================================
   UTILITY CLASSES
   ============================================= */

/* Szybkie klasy dla różnych typów przejść */
.transition-smooth { transition: all 0.6s cubic-bezier(0.25, 0.1, 0.25, 1); }
.transition-organic { transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1); }
.transition-elegant { transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94); }

/* Opóźnienia dla staggered animacji */
.transition-delay-100 { transition-delay: 100ms; }
.transition-delay-200 { transition-delay: 200ms; }
.transition-delay-300 { transition-delay: 300ms; }
.transition-delay-400 { transition-delay: 400ms; }
.transition-delay-500 { transition-delay: 500ms; }